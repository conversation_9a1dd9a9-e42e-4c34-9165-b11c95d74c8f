#!/usr/bin/env python3
"""
Example usage of the Palm Disease Classification API with treatment suggestions.

This script demonstrates how to use both endpoints:
1. Basic prediction endpoint
2. Prediction with treatment suggestions endpoint

Make sure to:
1. Start the Flask server: python app.py
2. Set OPENAI_API_KEY environment variable for treatment suggestions
3. Have a test image ready
"""

import requests
import json
import os

# Configuration
API_BASE_URL = "http://127.0.0.1:5000"
TEST_IMAGE_PATH = "test_image.jpg"  # Update this path to your test image

def test_basic_prediction(image_path):
    """Test the basic prediction endpoint."""
    print("=== Testing Basic Prediction ===")
    
    url = f"{API_BASE_URL}/predict"
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, files=files, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("Prediction Result:")
            print(json.dumps(result, indent=2))
        else:
            print("Error:", response.json())
            
    except Exception as e:
        print(f"Error: {e}")

def test_prediction_with_treatment(image_path):
    """Test the prediction with treatment suggestions endpoint."""
    print("\n=== Testing Prediction with Treatment Suggestions ===")
    
    url = f"{API_BASE_URL}/predict-with-treatment"
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, files=files, timeout=60)  # Longer timeout for AI processing
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("Prediction with Treatment Result:")
            print(json.dumps(result, indent=2))
            
            # Display treatment suggestion in a more readable format
            if 'treatment_suggestion' in result:
                print("\n" + "="*50)
                print("TREATMENT SUGGESTION:")
                print("="*50)
                print(result['treatment_suggestion'])
                if 'disclaimer' in result:
                    print("\nDISCLAIMER:")
                    print(result['disclaimer'])
        else:
            print("Error:", response.json())
            
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Main function to run the examples."""
    print("Palm Disease Classification API - Example Usage")
    print("=" * 50)
    
    # Check if test image exists
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"Error: Test image not found at {TEST_IMAGE_PATH}")
        print("Please update TEST_IMAGE_PATH or create a test image.")
        return
    
    # Check if API is running
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print("Error: API is not responding correctly.")
            return
    except requests.exceptions.ConnectionError:
        print("Error: Cannot connect to API. Make sure the Flask server is running.")
        print("Start it with: python app.py")
        return
    
    print("API is running. Starting tests...\n")
    
    # Test basic prediction
    test_basic_prediction(TEST_IMAGE_PATH)
    
    # Test prediction with treatment
    test_prediction_with_treatment(TEST_IMAGE_PATH)
    
    print("\n" + "="*50)
    print("Testing completed!")
    
    # Check if OpenAI API key is set
    if not os.getenv('OPENAI_API_KEY'):
        print("\nNote: OPENAI_API_KEY environment variable is not set.")
        print("Treatment suggestions will show a fallback message.")
        print("To enable AI treatment suggestions, set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")

if __name__ == "__main__":
    main()
