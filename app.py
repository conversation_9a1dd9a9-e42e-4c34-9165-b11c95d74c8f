from flask import Flask, request, jsonify
import utils # Your utility functions


app = Flask(__name__)

# --- Configuration ---
# Ensure the UPLOAD_FOLDER exists if you plan to save files temporarily (not strictly needed for this BytesIO approach)
# UPLOAD_FOLDER = 'temp_uploads' 
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)
# app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Load the model when the Flask app starts
try:
    utils.load_model()
    print("Model loading initiated by app.py startup.")
except Exception as e:
    print(f"CRITICAL: Failed to load model on startup: {e}")
    # Depending on desired behavior, you might want the app to not start
    # or handle requests gracefully by returning an error.

@app.route('/predict', methods=['POST'])
def predict():
    if utils.model_instance is None:
        # This is a fallback if the initial load_model() failed.
        return jsonify({"error": "Model is not loaded. Server configuration issue."}), 500

    if 'file' not in request.files:
        return jsonify({"error": "No image file provided."}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({"error": "No selected file."}), 400
        
    if file:
        try:
            # Read image bytes directly
            image_bytes = file.read()
            prediction_result = utils.predict_image_data(image_bytes)
            
            if "error" in prediction_result:
                 return jsonify(prediction_result), 500 # Internal server error if prediction failed
            
            return jsonify(prediction_result), 200
            
        except Exception as e:
            print(f"Error processing file: {e}")
            return jsonify({"error": f"Error processing image: {str(e)}"}), 500
            
    return jsonify({"error": "Unknown error occurred."}), 500

@app.route('/', methods=['GET'])
def home():
    return "Palm Disease Classification API is running. Use POST /predict to classify an image."

if __name__ == '__main__':
    # For development:
    # Flask's built-in server is not recommended for production.
    # Use Gunicorn or another WSGI server in production.
    app.run(debug=True, host='0.0.0.0', port=5000)
