from ultralytics import YOLO
from PIL import Image
import os
import io

# --- Configuration ---
MODEL_PATH = "best.pt" # Path to your trained model

# This list is a fallback if model.names is not available.
# The API will prioritize model.names if present.
FALLBACK_CLASS_NAMES = [
    "Black Scorch", "brown spots", "BUG", "Dubas", "Fusarium Wilt",
    "Healthy", "Honey", "Leaf Spots", "Magnesium Deficiency",
    "Manganese Deficiency", "Potassium Deficiency", "Rachis Blight", "white scale"
]

model_instance = None
model_class_names = None
class_names_source = "Not loaded" # This variable will still be used internally for logging/debugging if needed

def load_model():
    """Loads the YOLOv8 classification model instance and its class names."""
    global model_instance, model_class_names, class_names_source
    if not os.path.exists(MODEL_PATH):
        print(f"ERROR: Model file not found at {MODEL_PATH}")
        raise FileNotFoundError(f"Model file not found at {MODEL_PATH}")
    
    try:
        model_instance = YOLO(MODEL_PATH)
        print(f"Model loaded successfully from {MODEL_PATH}")

        # Attempt to load class names from the model
        if hasattr(model_instance, 'names') and isinstance(model_instance.names, dict) and model_instance.names:
            model_class_names = model_instance.names
            class_names_source = "model.names"
            print(f"Class names loaded from model.names: {model_class_names}")
        else:
            model_class_names = {i: name for i, name in enumerate(FALLBACK_CLASS_NAMES)}
            class_names_source = "fallback_list"
            print(f"Using fallback class names: {model_class_names}")

    except Exception as e:
        print(f"Error loading model or class names: {e}")
        if model_class_names is None: # Ensure fallback if error during name loading
            model_class_names = {i: name for i, name in enumerate(FALLBACK_CLASS_NAMES)}
            class_names_source = "fallback_list_after_error"
        raise e

def predict_image_data(image_bytes):
    """
    Performs inference on the image bytes and returns structured top-1 prediction data.
    """
    global model_instance, model_class_names # class_names_source is no longer directly returned

    if model_instance is None:
        print("ERROR: Model not loaded. Attempting to load now.")
        load_model() 
        if model_instance is None:
             raise RuntimeError("Model could not be loaded.")

    try:
        image = Image.open(io.BytesIO(image_bytes))
        results = model_instance(image) 

        if results and results[0].probs is not None:
            top1_idx = results[0].probs.top1
            top1_confidence = results[0].probs.top1conf.item()
            
            predicted_class_name = model_class_names.get(top1_idx, f"Unknown Index {top1_idx}")
            
            # Return only top-1 prediction without model_class_names_source
            return {
                "predicted_class": predicted_class_name,
                "confidence": round(top1_confidence, 4)
            }
        else:
            return {"error": "Model did not return valid probabilities."}
            
    except Exception as e:
        print(f"Error during prediction: {e}")
        return {"error": f"Prediction error: {str(e)}"}

