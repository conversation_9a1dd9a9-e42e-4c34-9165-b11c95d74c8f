from ultralytics import YOLO
from PIL import Image
import os
import io
from openai import OpenAI

# --- Configuration ---
MODEL_PATH = "best.pt" # Path to your trained model

# OpenAI Configuration
# Set your OpenAI API key as an environment variable: OPENAI_API_KEY
# or uncomment and set it directly (not recommended for production)
# OPENAI_API_KEY = "your-api-key-here"
openai_client = None

# This list is a fallback if model.names is not available.
# The API will prioritize model.names if present.
FALLBACK_CLASS_NAMES = [
    "Black Scorch", "brown spots", "BUG", "Dubas", "Fusarium Wilt",
    "Healthy", "Honey", "Leaf Spots", "Magnesium Deficiency",
    "Manganese Deficiency", "Potassium Deficiency", "Rachis Blight", "white scale"
]

model_instance = None
model_class_names = None
class_names_source = "Not loaded" # This variable will still be used internally for logging/debugging if needed

def initialize_openai_client():
    """Initialize OpenAI client with API key from environment variable."""
    global openai_client
    try:
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            openai_client = OpenAI(api_key=api_key)
            print("OpenAI client initialized successfully.")
        else:
            print("WARNING: OPENAI_API_KEY environment variable not set. Treatment suggestions will be disabled.")
    except Exception as e:
        print(f"Error initializing OpenAI client: {e}")
        openai_client = None

def load_model():
    """Loads the YOLOv8 classification model instance and its class names, and initializes OpenAI client."""
    global model_instance, model_class_names, class_names_source
    if not os.path.exists(MODEL_PATH):
        print(f"ERROR: Model file not found at {MODEL_PATH}")
        raise FileNotFoundError(f"Model file not found at {MODEL_PATH}")

    try:
        model_instance = YOLO(MODEL_PATH)
        print(f"Model loaded successfully from {MODEL_PATH}")

        # Attempt to load class names from the model
        if hasattr(model_instance, 'names') and isinstance(model_instance.names, dict) and model_instance.names:
            model_class_names = model_instance.names
            class_names_source = "model.names"
            print(f"Class names loaded from model.names: {model_class_names}")
        else:
            model_class_names = {i: name for i, name in enumerate(FALLBACK_CLASS_NAMES)}
            class_names_source = "fallback_list"
            print(f"Using fallback class names: {model_class_names}")

        # Initialize OpenAI client for treatment suggestions
        initialize_openai_client()

    except Exception as e:
        print(f"Error loading model or class names: {e}")
        if model_class_names is None: # Ensure fallback if error during name loading
            model_class_names = {i: name for i, name in enumerate(FALLBACK_CLASS_NAMES)}
            class_names_source = "fallback_list_after_error"
        raise e

def get_treatment_suggestion(disease_name, confidence_score):
    """
    Get treatment suggestions for a detected palm disease using ChatGPT 3.5 Turbo.

    Args:
        disease_name (str): Name of the detected disease
        confidence_score (float): Confidence score of the prediction

    Returns:
        dict: Treatment suggestion response or error message
    """
    global openai_client

    if openai_client is None:
        return {
            "treatment_suggestion": "Treatment suggestions unavailable - OpenAI API not configured.",
            "error": "OpenAI client not initialized"
        }

    # Skip treatment suggestions for healthy palms
    if disease_name.lower() == "healthy":
        return {
            "treatment_suggestion": "Your palm tree appears healthy! Continue with regular care including proper watering, fertilization, and monitoring for any changes.",
            "confidence_note": f"Prediction confidence: {confidence_score:.2%}"
        }

    # Create a detailed prompt for treatment suggestions
    prompt = f"""You are an expert agricultural consultant specializing in palm tree diseases.

A palm tree has been diagnosed with: {disease_name}
Prediction confidence: {confidence_score:.2%}

Please provide:
1. A brief description of this condition
2. Immediate treatment steps
3. Prevention measures
4. When to seek professional help

Keep the response concise but comprehensive, suitable for palm tree owners. Focus on practical, actionable advice."""

    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an expert agricultural consultant specializing in palm tree health and disease management."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.7
        )

        treatment_text = response.choices[0].message.content.strip()

        return {
            "treatment_suggestion": treatment_text,
            "confidence_note": f"Prediction confidence: {confidence_score:.2%}",
            "disclaimer": "This is AI-generated advice. For severe cases or persistent problems, consult a certified arborist or agricultural extension service."
        }

    except Exception as e:
        print(f"Error getting treatment suggestion from OpenAI: {e}")
        return {
            "treatment_suggestion": f"Unable to generate treatment suggestion for {disease_name}. Please consult a local agricultural expert.",
            "error": f"OpenAI API error: {str(e)}"
        }

def predict_image_data(image_bytes, include_treatment=False):
    """
    Performs inference on the image bytes and returns structured top-1 prediction data.

    Args:
        image_bytes: Image data as bytes
        include_treatment (bool): Whether to include AI-generated treatment suggestions

    Returns:
        dict: Prediction results with optional treatment suggestions
    """
    global model_instance, model_class_names # class_names_source is no longer directly returned

    if model_instance is None:
        print("ERROR: Model not loaded. Attempting to load now.")
        load_model() 
        if model_instance is None:
             raise RuntimeError("Model could not be loaded.")

    try:
        image = Image.open(io.BytesIO(image_bytes))
        results = model_instance(image) 

        if results and results[0].probs is not None:
            top1_idx = results[0].probs.top1
            top1_confidence = results[0].probs.top1conf.item()
            
            predicted_class_name = model_class_names.get(top1_idx, f"Unknown Index {top1_idx}")

            # Prepare base response
            result = {
                "predicted_class": predicted_class_name,
                "confidence": round(top1_confidence, 4)
            }

            # Add treatment suggestions if requested
            if include_treatment:
                treatment_info = get_treatment_suggestion(predicted_class_name, top1_confidence)
                result.update(treatment_info)

            return result
        else:
            return {"error": "Model did not return valid probabilities."}
            
    except Exception as e:
        print(f"Error during prediction: {e}")
        return {"error": f"Prediction error: {str(e)}"}

