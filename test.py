import requests
import os

# API endpoint URLs
API_URL_PREDICT = "http://127.0.0.1:5000/predict"
API_URL_TREATMENT = "http://127.0.0.1:5000/predict-with-treatment"

# Path to your test image (create a dummy image or use a real one)
# Ensure this image exists in athe same directory as test.py or provide the full path.
TEST_IMAGE_PATH = "test_image.jpg" 

def test_api_with_image(image_path, api_url, endpoint_name):
    """
    Sends an image to the API and prints the response.
    """
    if not os.path.exists(image_path):
        print(f"Error: Test image not found at {image_path}")
        print("Please create a 'test_image.jpg' or update TEST_IMAGE_PATH.")
        return

    print(f"\n--- Testing {endpoint_name} ---")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')} # Or image/png
            response = requests.post(api_url, files=files, timeout=30) # Added timeout

        print(f"Status Code: {response.status_code}")
        try:
            print("Response JSON:")
            response_data = response.json()
            # Pretty print the response
            import json
            print(json.dumps(response_data, indent=2))
        except requests.exceptions.JSONDecodeError:
            print("Response Content (not JSON):")
            print(response.text)

    except requests.exceptions.ConnectionError as e:
        print(f"Connection Error: Could not connect to the API at {api_url}.")
        print("Please ensure the Flask server (app.py) is running.")
        print(f"Details: {e}")
    except requests.exceptions.Timeout:
        print(f"Request timed out after 30 seconds. The server might be slow or unresponsive.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    print(f"Testing Palm Disease Classification API with image: {TEST_IMAGE_PATH}")

    # Create a dummy test image if it doesn't exist for basic testing
    if not os.path.exists(TEST_IMAGE_PATH):
        try:
            from PIL import Image, ImageDraw
            img = Image.new('RGB', (100, 100), color = (255, 0, 0)) # Create a dummy red image
            d = ImageDraw.Draw(img)
            d.text((10,10), "Test", fill=(255,255,0))
            img.save(TEST_IMAGE_PATH, "JPEG")
            print(f"Created a dummy '{TEST_IMAGE_PATH}' for testing.")
        except ImportError:
            print(f"Pillow (PIL) not found. Could not create dummy test image. Please provide '{TEST_IMAGE_PATH}'.")
        except Exception as e_img:
            print(f"Could not create dummy test image: {e_img}. Please provide '{TEST_IMAGE_PATH}'.")

    if os.path.exists(TEST_IMAGE_PATH):
        # Test basic prediction endpoint
        test_api_with_image(TEST_IMAGE_PATH, API_URL_PREDICT, "Basic Prediction")

        # Test prediction with treatment suggestions
        test_api_with_image(TEST_IMAGE_PATH, API_URL_TREATMENT, "Prediction with Treatment Suggestions")

        print("\nNote: Treatment suggestions require OPENAI_API_KEY environment variable.")
        print("If not set, you'll see a message about treatment suggestions being unavailable.")
    else:
        print(f"Skipping test as '{TEST_IMAGE_PATH}' could not be found or created.")
