# Palm Disease Classification API

A Flask-based API for classifying palm tree diseases using a YOLOv8 model.

## Features

- Image-based disease classification
- RESTful API endpoint for predictions
- Supports 13 different palm disease/condition classes
- AI-powered treatment suggestions using ChatGPT 3.5 Turbo

## Setup

1. Clone this repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Ensure you have the trained model file (`best.pt`) in the project root
4. (Optional) Set your OpenAI API key for treatment suggestions:
   ```
   export OPENAI_API_KEY="your-api-key-here"
   ```

## Usage

1. Start the server:
   ```
   python app.py
   ```
2. The API will be available at http://127.0.0.1:5000

### API Endpoints

- `GET /`: Health check endpoint
- `POST /predict`: Submit an image for classification
  - Accepts form data with a file field named 'file'
  - Returns JSON with predicted class and confidence score
- `POST /predict-with-treatment`: Submit an image for classification with AI treatment suggestions
  - Accepts form data with a file field named 'file'
  - Returns JSON with predicted class, confidence score, and AI-generated treatment suggestions
  - Requires OPENAI_API_KEY environment variable to be set

## Testing

Run the test script to verify the API is working:
```
python test.py
```

## Classes

The model can detect the following palm conditions:
- Black Scorch
- Brown Spots
- BUG
- Dubas
- Fusarium Wilt
- Healthy
- Honey
- Leaf Spots
- Magnesium Deficiency
- Manganese Deficiency
- Potassium Deficiency
- Rachis Blight
- White Scale